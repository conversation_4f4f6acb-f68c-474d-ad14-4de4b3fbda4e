# rn_mcp.py
import os
from typing import List
from mcp.server.fastmcp import FastMCP

# Initialize MCP server
mcp = FastMCP("rn-mcp")

RN_APP_PATH = "/Users/<USER>/Downloads/roundswebwindowsfinalversion"

def find_screens() -> List[str]:
    """Return all JS/TS files representing screens (e.g., containing 'Screen' in filename)."""
    screens = []
    for root, _, files in os.walk(RN_APP_PATH):
        for f in files:
            if f.endswith((".js", ".jsx", ".ts", ".tsx")) and "Screen" in f:
                rel = os.path.relpath(os.path.join(root, f), RN_APP_PATH)
                screens.append(rel)
    return screens

@mcp.tool()
def list_screens() -> List[str]:
    """
    List all screen files in the RN app.
    """
    return find_screens()

@mcp.tool()
def get_screen_content(screen_path: str) -> str:
    """
    Return content of the given screen file.
    """
    full_path = os.path.join(RN_APP_PATH, screen_path)
    if not os.path.isfile(full_path):
        raise FileNotFoundError(f"Screen not found: {screen_path}")
    with open(full_path, "r", encoding="utf-8") as f:
        return f.read()

@mcp.tool()
def get_component_tree(screen_path: str) -> dict:
    """
    Return a simplistic component-tree-like representation.
    (Better suited if you parse AST for real structure.)
    """
    content = get_screen_content(screen_path)
    # Naïvely count React Native component tags
    tree = {}
    for tag in ["View", "Text", "Image", "TouchableOpacity"]:
        tree[tag] = content.count(f"<{tag}")
    return tree

if __name__ == "__main__":
    mcp.run(transport="stdio")